
<div class="particle-information-container" *ngIf="!custom?.selectedParticleId">
  <div class="particle-content">
    <div class="text-center">
      <h2 style="color: #7f8c8d; font-weight: 300;">No particle selected!</h2>
      <p style="color: #95a5a6;">Please select a particle from the particle list to view its information.</p>
    </div>
  </div>
</div>

<div class="particle-information-container" *ngIf="custom?.selectedParticleId">
  <div class="card list-header-row">
    <app-particle-navigation-header class="card"
                                   [isBackButtonEnabled]="true"
                                   (cardBackButtonClick)="onBack()"
                                   [cardTitle]="item?.name"
                                   [cardDescription]="item?.description"
                                   [rightButtonTemplates]="[leftButtonTemplate, rightButtonTemplate]"
                                   [currentIndex]="currentParticleIndex"
                                   [totalItems]="particleItemList.length"
                                   [showNavigationInfo]="true">
    </app-particle-navigation-header>
  </div>
  <div class="particle-content">
    <div class="two-column-layout">
      <div class="particle-section">
        <h3 class="particle-section-title">Particle Information</h3>

        <div class="form-group">
          <label class="form-label">Battle Description:</label>
          <textarea class="form-control textarea"
            placeholder="Enter battle description..."
            [value]="(particle | translation : lstLanguage : particle.id : 'description')"
            #description
            (change)="changeDescription(description.value)">
          </textarea>
        </div>

        <div class="form-group">
          <label class="form-label">Effect Option:</label>
          <select class="form-control select"
                  [(ngModel)]="particle.effectId"
                  #effectId
                  (change)="changeSelectedEffectId(effectId.value)">
            <option value="" disabled>Select an effect...</option>
            <option *ngFor="let effect of effects"
                    [value]="effect.id">{{ effect.name }}</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">HC Level (Gating.HC):</label>
          <input type="number"
                 class="form-control number-input"
                 placeholder="Enter HC level"
                 [(ngModel)]="particle.hcLevel"
                 (change)="changeHcLevel(particle.hcLevel)"/>
        </div>

        <div class="form-group">
          <label class="form-label">ATK:</label>
          <input type="number"
                 class="form-control number-input"
                 placeholder="Enter ATK value"
                 [(ngModel)]="particle.atk"
                 (change)="changeAtk(particle.atk)"/>
        </div>

        <div class="form-group">
          <label class="form-label">Splits:</label>
          <input type="number"
                 class="form-control number-input"
                 placeholder="Enter splits value"
                 [(ngModel)]="particle.split"
                 (change)="changeSplit(particle.split)"/>
        </div>

        <div class="form-group">
          <label class="form-label">Properties:</label>
          <div class="checkbox-group">
            <div class="custom-checkbox">
              <input type="checkbox"
                     id="shake-checkbox"
                     [(ngModel)]="particle.shake"
                     (change)="changeShake(particle.shake)"/>
              <label for="shake-checkbox" class="checkbox-label">Shake</label>
            </div>
            <div class="custom-checkbox">
              <input type="checkbox"
                     id="hit-checkbox"
                     [(ngModel)]="particle.hit"
                     (change)="changeHit(particle.hit)"/>
              <label for="hit-checkbox" class="checkbox-label">Hit</label>
            </div>
          </div>
        </div>
      </div>

      <div class="particle-section">
        <h3 class="particle-section-title">Assignments</h3>

        <div class="form-group">
          <label class="form-label">Character Group:</label>
          <div class="selector-container">
            <character-selector
              [selectedCharactersId]="particle.charactersId"
              (selectedCharactersIdChange)="changeSelectedCharactersId($event)">
            </character-selector>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Classes:</label>
          <div class="selector-container">
            <class-selector
              [selectedClassessId]="particle.classesId"
              (selectedClassessIdChange)="changeSelectedClassessId($event)">
            </class-selector>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
