<div class="main-content">
  <div class="container-fluid">
    <div class="list-header-row update">
      <div class="card" style="padding-bottom: 2px;">
        <div class="card-header-content" style="position: absolute; top: 14px;">
          <h3 class="title">{{listName}}{{textElement ? ' - ' + textElement : ''}}</h3>
          <p style="width:60vw;" class="category">{{ description}}</p>
        </div>

        <div style="display: flex; align-items: end; justify-content: end; margin-right: 92px;">
          <div style="margin-right: 17px; margin-bottom: 16px;">
            <button class="btn btn-fill selectedButton">
              Particles
            </button>
            <button (click)="sendActiveTab('Ingredients')"
              class="{{activeTab === 'Ingredients' ? 'btn btn-fill selectedButton btn-space-buttom-5' : 'btn btn-fill btn-space-buttom-5'}}">
              Ingredients
            </button>
          </div>
        </div>

        <div style="position: relative; margin-bottom: 3px;">
          <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
            [buttonTemplates]="[excelButtonTemplate]">
          </app-button-group>
        </div>

      </div>
    </div>


    <ng-container *ngIf="activeTab != undefined">
    <div style="overflow-x: auto;">
      <table class="table-list">
        <thead class="sticky">
          <tr>
            <th class="col_index">Index</th>
            <th class="th-clickable titleParticle" (click)="sortListByParameter('type')">Particle</th>
            <th *ngFor="let areaOrder of areas; let i = index" class="th-clickable">{{ areaOrder }}</th>
            <th class="th-clickable" (click)="lineupOrderDropsCharacter()">Drop By Character</th>
          </tr>
        </thead>
        <tbody style="background-color: white;">
          <ng-container *ngFor="let particle of this.particlesList; let i = index;">
            <tr id="{{ particle.name }}" *ngIf="particle.type === this.ingredientType">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id contentParticle">{{ particle.name }}</td>
              <td class="td-id" *ngFor="let area of areas; let i = index">
                <input *ngIf="i != areas.length"
                  (change)="changeIngredientValue(InputCommonProbability.value, particle, i, area)" type="number"
                  value="{{particle?.order[i] != undefined && +particle?.order[i] == +area && particle?.amount[i] != undefined ? +particle?.amount[i] : undefined}}"
                  #InputCommonProbability style="border-style:solid; top:-1000px !important;"
                  [ngClass]="{'empty-input': !InputCommonProbability.value}" />
              </td>
              <td>
                <select [ngClass]="{'empty-input': particle.drop === undefined || particle.drop === ''}"
                  #InputCommonProbability style="border-style:solid;"
                  (change)="changeIngredientDrop(InputCommonProbability.value, particle)">
                  <option style="color: black" *ngFor="let option of dropsOptions" [value]="option"
                    [selected]="particle.drop === option">{{ option }}
                  </option>
                </select>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
</ng-container>
  </div>
</div>