
// Modern particle information styling
.particle-information-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 20px;
  min-height: calc(100vh - 200px);
}

.particle-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.particle-section {
  margin-bottom: 32px;
}

.particle-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
}

.particle-section-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 2px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 8px;
  font-size: 1.1rem;
  letter-spacing: 0.3px;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: #fff;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  transform: translateY(-1px);
}

.form-control.textarea {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.form-control.select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.number-input {
  max-width: 200px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.custom-checkbox {
  position: relative;
  display: inline-block;
}

.custom-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 20px;
  height: 20px;
  margin: 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #34495e;
}

.checkbox-label::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #bdc3c7;
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.3s ease;
  background: white;
}

.custom-checkbox input[type="checkbox"]:checked + .checkbox-label::before {
  background: #3498db;
  border-color: #3498db;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.custom-checkbox input[type="checkbox"]:focus + .checkbox-label::before {
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.two-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 20px;
}

.selector-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

// Legacy support for existing elements
h4 {
  display: block;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

input[type='checkbox'] {
  margin-left: 10px;
  margin-bottom: 10px;
  width: 20px;
  height: 20px;
}

// Responsive design
@media (max-width: 768px) {
  .particle-information-container {
    margin: 10px;
    padding: 15px;
  }

  .particle-content {
    padding: 20px;
  }

  .two-column-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .particle-section-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .particle-information-container {
    margin: 5px;
    padding: 10px;
  }

  .particle-content {
    padding: 15px;
  }

  .form-control {
    padding: 10px 12px;
  }
}
