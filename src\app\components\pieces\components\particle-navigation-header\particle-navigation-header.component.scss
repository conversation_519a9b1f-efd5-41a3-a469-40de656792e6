.card-header-wrapper {
  padding: 15px;
  padding-right: 150px; /* Make room for navigation buttons */
  display: flex;
  align-items: flex-start;
  position: relative;
  min-height: 60px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header-content {
  display: block;
  margin-left: 10px;
  margin-right: 15px;
  width: 35%;
}

.card-header-content .title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8px;
}

.card-header-content .category {
  color: #6c757d;
  font-size: 0.9em;
  line-height: 1.4;
}

.card-header-tooltip {
  display: block;
  margin-left: 33%;
  margin-right: 33%;
  margin-bottom: 50px;
  color: white !important;
  background-color: black;
  padding: 30px;
  z-index: 999;
  border-radius: 10px;
}

.card-title-desccription {
  width: 100%;
  padding-bottom: 30px;
}

.text-description {
  height: 30px;
  white-space: normal;
  word-wrap: break-word;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: 100%;
  min-height: 100% !important;
}

.btnSubContext {
  position: absolute;
  top: 25px;
  right: 120px; 
}

.textTitles {
  display: flex;
  align-items: center;
  width: 30%;
  margin-left: 60px;
  gap: 10px;
}

.c-nameRarity {
  margin-left: 5px;
  padding: 6px;
  margin-top: -6px;
  border-radius: 5px;
}

.navigation-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  margin: 0 20px;
}

.navigation-info {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: rgba(52, 152, 219, 0.15);
  border: 2px solid rgba(52, 152, 219, 0.4);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.nav-counter {
  font-size: 0.95em;
  font-weight: 600;
  color: #2980b9;
  letter-spacing: 0.5px;
}

/* Fixed button positioning for particle navigation header */
.particle-navigation-buttons {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 100;
  min-width: 120px;
  /* Temporary debugging background */
  background: rgba(255, 0, 0, 0.1);
  border: 1px dashed red;
  padding: 5px;
}

.particle-navigation-buttons .btn-group {
  background: rgba(0, 255, 0, 0.1);
  border: 1px dashed green;
  padding: 2px;
}

.particle-navigation-buttons .btn {
  background: rgba(0, 0, 255, 0.2) !important;
  border: 1px solid blue !important;
  color: black !important;
  min-width: 40px !important;
  min-height: 30px !important;
  margin: 2px !important;
}

.particle-navigation-buttons .btn-group {
  display: flex;
  padding-top: 6px;
  gap: 8px;
}

.particle-navigation-buttons .btn {
  margin-left: 5px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.particle-navigation-buttons .btn:first-child {
  margin-left: 0;
}

.particle-navigation-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 768px) {
  .card-header-wrapper {
    flex-direction: column;
    align-items: stretch;
  }
  
  .card-header-content {
    width: 100%;
    margin-bottom: 15px;
  }
  
  .textTitles {
    width: 100%;
    margin-left: 0;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .navigation-center {
    margin: 15px 0;
    justify-content: center;
  }

  .particle-navigation-buttons {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 15px;
    align-self: flex-end;
  }
}
