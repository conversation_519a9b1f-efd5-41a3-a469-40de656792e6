import { Component, OnInit } from '@angular/core';
import { Effect, Item, Weapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { Particle } from 'src/app/lib/@bus-tier/models/Particle';
import { EffectService, ItemService, ParticleService, UserSettingsService, WeaponService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { LanguageService } from 'src/app/services/language.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ActivatedRoute, Router } from '@angular/router';
import { Button } from 'src/app/lib/@pres-tier/data';

@Component({
  selector: 'app-particle-information',
  templateUrl: './particle-information.component.html',
  styleUrls: ['./particle-information.component.scss'],
})
export class ParticleInformationComponent  extends TranslatableListComponent<Particle> implements OnInit {

  constructor(
    protected _customService: CustomService,
    protected override _languageService: LanguageService,
    private _itemService: ItemService,
    private _particleService: ParticleService,
    private _weaponService: WeaponService,
    private _effectService: EffectService,
    private _router: Router,
    private _itemClassService: ItemClassService,
    protected override _translationService: TranslationService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
  )
  { super(_particleService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);}

  override lstLanguage: string;
  custom: Custom;
  item: Item;
  particle: Particle;
  weapons: Weapon[];
  effects: Effect[];
  itemList: Item[] = [];

  // Navigation properties
  particleItemList: Item[] = [];
  currentParticleIndex: number = 0;

  override async ngOnInit(): Promise<void>
  {
    await this._particleService.toFinishLoading();
    await this._weaponService.toFinishLoading();
    await this._effectService.toFinishLoading();
    this.effects = this._effectService.models;
    this.weapons = this._weaponService.models;
    this.lstLanguage = this._languageService.activeLanguage.name;

    this.custom = await this._customService.svcGetInstance();

    // Build the particle item list for navigation
    await this.buildParticleItemList();

    this.item = this._itemService.svcFindById(this.custom.selectedParticleId);
    this.particle = this._particleService.models.find(w => w.itemId === this.custom.selectedParticleId
    )
    if(!this.particle)
    {
      this.particle = this._particleService.createNewWeapon(this.custom.selectedParticleId);
    }

    // Find current index in the particle list
    this.currentParticleIndex = this.particleItemList.findIndex(item => item.id === this.custom.selectedParticleId);
    if (this.currentParticleIndex === -1) this.currentParticleIndex = 0;
  }

 /*  public downloadMapsOrtography(particle: Particle) {
    this._translationService.getParticleOrtography(particle, true);
  } */

  public GetItemName(itemId: string): string
  {
    return this._itemService.svcFindById(itemId).name;
  }

  public onBack()
  {
    this._router.navigate(['laboratories']);
  }

  async changeWeaponId(weaponId: string)
  {
    this.particle.weaponId = weaponId;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeDescription(description: string)
  {
    this.particle.description = description;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeShake(shake: boolean)
  {
    this.particle.shake = shake;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeHit(hit: boolean)
  {
    this.particle.hit = hit;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeSplit(split: number)
  {
    this.particle.split = split;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeHcLevel(hcLevel: number)
  {
    this.particle.hcLevel = hcLevel;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeAtk(atk: number)
  {
    this.particle.atk = atk;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeSelectedCharactersId(selectedCharactersId: string[])
  {
    this.particle.charactersId = selectedCharactersId;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeSelectedClassessId(selectedClassessId: string[])
  {
    this.particle.classesId = selectedClassessId;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  async changeSelectedEffectId(effectId: string)
  {
    this.particle.effectId = effectId;
    await this._particleService.svcToModify(this.particle);
    this._particleService.toSave();
  }

  // Navigation methods
  private async buildParticleItemList(): Promise<void>
  {
    this.particleItemList = [];

    if (this.custom.particleClassItem) {
      this.custom.particleClassItem.forEach(itemClassId => {
        let itemClass = this._itemClassService.svcFindById(itemClassId);
        if (itemClass) {
          itemClass.itemIds.forEach(itemId => {
            let item = this._itemService.svcFindById(itemId);
            if (item && !this.particleItemList.find(existingItem => existingItem.id === item.id)) {
              this.particleItemList.push(item);
            }
          });
        }
      });
    }

    // Sort by name for consistent navigation
    this.particleItemList.sort((a, b) => a.name.localeCompare(b.name));
  }

  public readonly leftButtonTemplate: Button.Templateable =
  {
    title: 'Previous particle',
    onClick: this.leftButton.bind(this),
    iconClass: 'pe-7s-angle-left',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public readonly rightButtonTemplate: Button.Templateable =
  {
    title: 'Next particle',
    onClick: this.rightButton.bind(this),
    iconClass: 'pe-7s-angle-right',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  async leftButton() {
    if (this.particleItemList.length === 0) return;

    // If at the beginning, go to the last item
    if (this.currentParticleIndex === 0) {
      this.currentParticleIndex = this.particleItemList.length - 1;
    } else {
      this.currentParticleIndex = this.currentParticleIndex - 1;
    }

    await this.navigateToParticle();
  }

  async rightButton() {
    if (this.particleItemList.length === 0) return;

    // If at the end, go to the first item
    if (this.currentParticleIndex === this.particleItemList.length - 1) {
      this.currentParticleIndex = 0;
    } else {
      this.currentParticleIndex = this.currentParticleIndex + 1;
    }

    await this.navigateToParticle();
  }

  private async navigateToParticle(): Promise<void>
  {
    if (this.particleItemList.length === 0) return;

    const selectedItem = this.particleItemList[this.currentParticleIndex];
    await this._customService.setCustomField(selectedItem.id, 'selectedParticleId');

    // Reload the current particle data
    this.item = selectedItem;
    this.particle = this._particleService.models.find(w => w.itemId === selectedItem.id);
    if (!this.particle) {
      this.particle = this._particleService.createNewWeapon(selectedItem.id);
    }
  }
}
