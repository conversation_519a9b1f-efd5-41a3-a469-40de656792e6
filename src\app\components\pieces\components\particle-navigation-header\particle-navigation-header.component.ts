import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';
import { IndexStorageService } from 'src/app/services';

@Component({
  selector: 'app-particle-navigation-header',
  templateUrl: './particle-navigation-header.component.html',
  styleUrls: ['./particle-navigation-header.component.scss']
})
export class ParticleNavigationHeaderComponent implements OnInit {
  constructor(private _indexStorageService: IndexStorageService) { }

  @Input() cardTooltip: string = '';
  @Input() selectedOption: string = '';
  @Input() cardTitle: string;
  @Input() valueBossLevel: string;
  @Input() nameRarity: string;
  @Input() nameClass: string;
  @Input() type: string;
  @Input() cardDescription: string;
  @Input() btnSubContext = false;
  @Input() textDescriptionRecord = false;
  @Input() rightButtonTemplates: Button.Templateable[] = [];
  @Input() leftButtonTemplates: Button.Templateable[] = [];
  @Output() cardBackButtonClick = new EventEmitter();
  @Input() isBackButtonEnabled = false;
  @Input() hasDropdownSelect = false;
  activeLanguage = 'PTBR';
  @Input() elements = [];
  @Output() selectedElement = new EventEmitter<any>();
  @Output() clickBtn = new EventEmitter<any>();
  @Input() buttonLabel: string;
  @Input() isActive: boolean;

  // Navigation specific properties
  @Input() currentIndex: number = 0;
  @Input() totalItems: number = 0;
  @Input() showNavigationInfo: boolean = true;

  get titleBossLevel(): string {
    return this.nameClass ? `${this.valueBossLevel} - ` : this.valueBossLevel;
  }

  get navigationInfo(): string {
    if (!this.showNavigationInfo || this.totalItems === 0) return '';
    return `${this.currentIndex + 1} of ${this.totalItems}`;
  }

  ngOnInit(): void {
    this.activeLanguage = IndexStorageService.activeLanguage;
    this._indexStorageService.onLanguageChange$.subscribe(() => {
      this.activeLanguage = IndexStorageService.activeLanguage;
    });
  }

  changeElement(element: any): void {
    this.selectedElement.emit(element.target.value);
  }

  btnClickContext() {
    this.isActive = !this.isActive;
    this.clickBtn.emit(this.isActive);
  }
}
