<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update" style="display: flex; justify-content: space-between;">
      <div class="card" style="width: 100%; padding-bottom: 10px; align-items: center;">
        <div class="card-header-content">
          <h3 class="title">{{listName}}{{textElement ? ' - ' + textElement : ''}}</h3>
          <p style="width:60vw;" class="category">{{ description}}</p>
        </div>

        <ng-container *ngIf="!isModalInfo">
            <i ngClass="iconInter"
            (click)="onModalClick()" class="pe-7s-info batt"></i>         
        </ng-container>

        <!--Modal Info--> 
        <ng-container *ngIf="isModalInfo">              
          <div class="background-div handleOut" aria-hidden="true" >        
              <div id="modal-close" @popup class="popup-report" (mouseleave)="isModalInfo = false" style="background-color: black;">                
                  <div class="modal-header">                      
                        <div style="display: flex; justify-content: space-between;">
                          <p style="color:azure !important; text-align: center;" class="modal-title">Information</p>
                          <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()" data-dismiss="background-div" aria-label="Fechar"> 
                            <span aria-hidden="true">&times;</span>                               
                          </button> 
                         </div>                 
                            </div>                          
                        <div class="contextInfo"> 
                          <h6 style="color:azure !important; text-align: center;" class="modal-title title">These are items marked as ingredients
                            and having common ingredient tag (Ingrediente comum)
                         </h6>
                  </div>    
                </div>           
             </div>
        </ng-container>
        <!--Fim do Modal-->

        <div style="width: 30%; display: flex; justify-content: right; align-self: center;">
          <div style="display: flex; align-items: end; justify-content: end; margin-right: 36px;">
            <div style="margin-right: 13px; margin-bottom: 10px;">
              <button (click)="sendActiveTab('Particles')"
                class="{{activeTab === 'Particles' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
                Particles
              </button>
              <button  class="btn btn-fill btn-space-buttom-5 selectedButton">
                Ingredients
              </button>
            </div>
          </div>

        <div style="position: relative; top: 56px; margin-left: 60px;">
          <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
            [buttonTemplates]="[excelButtonTemplate]">
          </app-button-group>
        </div>
      </div>

      </div>
    </div>

    <ng-container *ngIf="activeTab != undefined">
    <div style="overflow-x: auto;">
      <table class="table-list">
        <thead class="sticky">
          <tr>
            <th class="col_index">Index</th>
            <th class="th-clickable titleIngredients" (click)="sortListByParameter('type')">Ingredient</th>
            <th *ngFor="let areaOrder of areas; let i = index" class="th-clickable">{{ areaOrder }}</th>
          </tr>
        </thead>
        <tbody style="background-color: white;">
          <ng-container *ngFor="let particle of this.particlesList; let i = index">
            <tr id="{{ particle.name }}" *ngIf="particle.type === this.ingredientType">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id contentIngredients">{{ particle.name }}</td>
              <td class="td-id" *ngFor="let area of areas; let i = index">
                <input *ngIf="i != areas.length"
                  (change)="changeIngredientValue(InputCommonProbability.value, particle, i, area)" type="number"
                  value="{{particle?.order[i] != undefined && +particle?.order[i] == +area && particle?.amount[i] != undefined ? +particle?.amount[i] : undefined}}"
                  #InputCommonProbability style="border-style:solid; top:-1000px !important;"
                  [ngClass]="{'empty-input': !InputCommonProbability.value}" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
    </ng-container>
  </div>
</div>